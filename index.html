<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="丰选科技是互联网产品全生命周期伙伴，提供从需求分析、产品开发到持续支持的全方位服务，助力产品创造持续价值。">
  <meta name="keywords" content="丰选,丰选科技,互联网产品,需求分析,产品开发,小程序开发,AI工具开发,数字产品,技术咨询,技术开发,技术运维">
  <meta name="author" content="丰选科技">
  <meta name="robots" content="index, follow">
  <meta property="og:title" content="丰选科技 - 互联网产品全生命周期伙伴">
  <meta property="og:description" content="丰选科技驱动互联网产品从规划到运维的全过程，提供真正的洞察与定制服务。">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://www.fengxuan.cn/">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="丰选科技 - 互联网产品全生命周期伙伴">
  <meta name="twitter:description" content="丰选科技驱动互联网产品从规划到运维的全过程，提供真正的洞察与定制服务。">
  <link rel="canonical" href="https://www.fengxuan.cn/">
  <title>丰选科技 - 互联网产品全生命周期伙伴</title>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary: #2c3e50;
      --secondary: #555;
      --accent: #00c853;
      --accent-gradient: linear-gradient(135deg, #00c853, #00a040);
      --light: #f8f9fa;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Noto Sans SC', 'PingFang SC', sans-serif;
      color: var(--primary);
      line-height: 1.8;
      font-weight: 400;
      -webkit-font-smoothing: antialiased;
      background-color: var(--light);
      transition: font-family 0.3s ease;
    }

    /* 英文字体样式 - 使用苹果字体 */
    body.lang-en {
      font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      line-height: 1.7;
    }

    body.lang-en .hero h1 {
      font-weight: 600;
      letter-spacing: -0.8px;
    }

    body.lang-en .hero p {
      font-weight: 400;
      letter-spacing: -0.2px;
    }

    body.lang-en .hero p.highlight-text {
      font-weight: 500;
      letter-spacing: -0.1px;
    }

    body.lang-en .contact h2 {
      font-weight: 600;
      letter-spacing: -0.8px;
    }

    body.lang-en .feature-card h3 {
      font-weight: 600;
      letter-spacing: -0.3px;
    }

    body.lang-en .feature-card p {
      font-weight: 400;
      letter-spacing: -0.1px;
      line-height: 1.6;
    }

    body.lang-en .subtitle {
      font-weight: 500;
      letter-spacing: -0.2px;
    }

    body.lang-en .lang-btn {
      font-weight: 500;
      letter-spacing: -0.1px;
    }

    body.lang-en .cta-button {
      font-weight: 600;
      letter-spacing: -0.2px;
    }

    /* 英文版本特殊布局调整 */
    body.lang-en .hero h1 {
      font-size: 2.8rem;
      line-height: 1.2;
    }

    body.lang-en .hero p {
      font-size: 1.2rem;
      margin-bottom: 18px;
    }

    body.lang-en .hero p.highlight-text {
      font-size: 1.3rem;
      padding: 12px 24px;
      margin-bottom: 44px;
    }

    body.lang-en .contact h2 {
      font-size: 2.1rem;
      line-height: 1.3;
    }

    body.lang-en .contact p {
      font-size: 1.05rem;
      margin-bottom: 16px;
    }

    body.lang-en .feature-card h3 {
      font-size: 1.4rem;
      margin-bottom: 12px;
    }

    body.lang-en .feature-card p {
      font-size: 0.95rem;
      line-height: 1.5;
    }

    body.lang-en .contact-method span {
      font-size: 1.05rem;
      font-weight: 500;
    }

    body.lang-en .email-container p {
      font-size: 1.05rem;
    }

    body.lang-en .email-contact {
      font-size: 1.15rem;
      font-weight: 600;
    }

    /* 英文版本文本优化 */
    body.lang-en .hero-content {
      max-width: 850px;
    }

    body.lang-en .contact p {
      max-width: 650px;
    }

    body.lang-en .feature-card {
      text-align: left;
    }

    body.lang-en .feature-card h3 {
      text-align: center;
    }

    body.lang-en .contact-method span {
      text-align: center;
    }

    /* 英文版本按钮优化 */
    body.lang-en .cta-button {
      padding: 15px 40px;
      font-size: 1.1rem;
    }

    /* 英文版本标题间距优化 */
    body.lang-en .hero h1::after {
      width: 120px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    header {
      padding: 20px 0;
      background-color: white;
      border-bottom: 2px solid var(--accent);
      position: sticky;
      top: 0;
      z-index: 1000;
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .logo {
      font-size: 28px;
      font-weight: 700;
      letter-spacing: 1px;
      color: var(--accent);
      transition: transform 0.3s ease;
    }

    .logo:hover {
      transform: scale(1.05);
    }

    .subtitle {
      font-size: 16px;
      font-weight: 400;
      color: var(--secondary);
      letter-spacing: 0.5px;
      padding-left: 15px;
      border-left: 2px solid var(--accent);
    }

    .hero {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      padding: 0;
      margin: 20px 0;
      border-radius: 8px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
      animation: fadeIn 0.8s ease-out forwards;
      overflow: hidden;
      /* 16:9比例控制 - 更宽更短的背景 */
      /* aspect-ratio: 16/9; */
      min-height: 70vh;
      width: 100%;
    }

    .hero::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.9)),
        url('https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80');
      background-size: cover;
      background-position: center;
      z-index: 0;
    }

    .hero::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
      animation: rotate 30s linear infinite;
      z-index: 2;
    }

    .hero-content {
      position: relative;
      z-index: 3;
      max-width: 800px;
      padding: 30px 20px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
    }

    .hero h1 {
      font-size: 3rem;
      margin-bottom: 24px;
      font-weight: 700;
      line-height: 1.3;
      letter-spacing: -0.5px;
      position: relative;
      padding-bottom: 15px;
      color: var(--accent);
    }

    .hero h1::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 3px;
      background: var(--accent-gradient);
    }

    .hero p {
      font-size: 1.25rem;
      max-width: 600px;
      margin-bottom: 20px;
      color: var(--secondary);
      font-weight: 300;
    }

    .hero p.highlight-text {
      font-size: 1.35rem;
      font-weight: 500;
      color: var(--accent);
      background-color: rgba(0, 200, 83, 0.1);
      padding: 10px 20px;
      border-radius: 6px;
      margin-bottom: 48px;
      display: inline-block;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      transform: translateY(0);
      transition: all 0.3s ease;
    }

    .hero p.highlight-text:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .cta-button {
      display: inline-block;
      padding: 14px 36px;
      background: var(--accent-gradient);
      color: white;
      text-decoration: none;
      border-radius: 30px;
      font-weight: 600;
      font-size: 1.1rem;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .cta-button:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
    }

    .contact {
      padding: 100px 0;
      text-align: center;
      background: linear-gradient(135deg, #f8f9fa, rgba(154, 190, 46, 0.1));
      border-radius: 8px;
      margin: 20px 0;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
      animation: fadeIn 0.8s ease-out forwards;
      animation-delay: 0.2s;
    }

    .contact h2 {
      font-size: 2.25rem;
      margin-bottom: 24px;
      font-weight: 600;
      letter-spacing: -0.5px;
      position: relative;
      padding-bottom: 0px;
      color: var(--accent);
    }

    /* 注释掉绿色横线
    .contact h2::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 3px;
      background: var(--accent-gradient);
    }
    */

    .contact p {
      max-width: 600px;
      margin: 0 auto 15px;
      /* 减小段落之间的间距 */
      color: var(--secondary);
      font-size: 1.1rem;
      font-weight: 300;
    }

    /* 为最后一个段落设置较大的底部外边距，与下方内容保持适当间距 */
    .contact p:last-of-type {
      margin-bottom: 48px;
    }

    .contact-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 30px;
      margin-bottom: 48px;
    }

    .contact-methods {
      display: flex;
      justify-content: center;
      gap: 60px;
      margin-bottom: 20px;
    }

    .contact-method {
      display: flex;
      flex-direction: column;
      align-items: center;
      transition: transform 0.3s ease;
    }

    .contact-method:hover {
      transform: translateY(-5px);
    }

    .qr-code-container {
      padding: 15px;
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .qr-code-container::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg, rgba(154, 190, 46, 0.1), rgba(122, 158, 30, 0.1), rgba(154, 190, 46, 0.1));
      animation: rotate 10s linear infinite;
      z-index: 1;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .qr-code-container:hover::before {
      opacity: 1;
    }

    .qr-code-container img {
      width: 140px;
      height: 140px;
      position: relative;
      z-index: 2;
      transition: transform 0.3s ease;
    }

    .qr-code-container:hover img {
      transform: scale(1.05);
    }

    .qr-code-container2 {
      /* padding: 5px; */
      /* background-color: white;
			border-radius: 12px; */
      /* box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); */
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .qr-code-container2 img {
      width: 100px;
      height: 50px;
      position: relative;
      z-index: 2;
      /* transition: transform 0.3s ease; */
    }

    .contact-method span {
      font-size: 1.1rem;
      color: var(--secondary);
      letter-spacing: 0.5px;
      margin-top: 16px;
      font-weight: 500;
    }

    .contact-divider {
      display: flex;
      align-items: center;
      width: 100%;
      max-width: 400px;
      margin: 10px 0;
    }

    .contact-divider::before,
    .contact-divider::after {
      content: '';
      flex: 1;
      height: 1px;
      background: rgba(0, 0, 0, 0.1);
    }

    .contact-divider span {
      padding: 0 15px;
      color: var(--secondary);
      font-size: 1rem;
    }

    .email-container {
      text-align: center;
      padding: 20px;
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease;
    }

    .email-container:hover {
      transform: translateY(-5px);
    }

    .email-container p {
      margin-bottom: 10px;
      color: var(--secondary);
      font-size: 1.1rem;
    }

    .email-contact {
      font-size: 1.2rem;
      color: var(--accent);
      text-decoration: none;
      transition: all 0.3s ease;
      padding: 10px 20px;
      border-radius: 30px;
      background-color: rgba(154, 190, 46, 0.1);
      display: inline-block;
      font-weight: 500;
    }

    .email-contact:hover {
      color: white;
      background: var(--accent-gradient);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    footer {
      padding: 15px 0;
      text-align: center;
      color: white;
      font-size: 0.9rem;
      letter-spacing: 0.5px;
      background-color: var(--accent);
      border-radius: 8px;
      margin: 20px 0;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    /* 添加动画效果 */
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }

    /* 特色卡片样式 */
    .features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 30px;
      margin: 20px 0;
      padding: 20px;
      animation: fadeIn 0.8s ease-out forwards;
      animation-delay: 0.4s;
    }

    .feature-card {
      background-color: white;
      border-radius: 8px;
      padding: 30px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .feature-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }

    .feature-icon {
      margin-bottom: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: var(--accent-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .feature-card h3 {
      font-size: 1.5rem;
      margin-bottom: 15px;
      color: var(--primary);
    }

    .feature-card p {
      color: var(--secondary);
      font-size: 1rem;
      line-height: 1.6;
    }

    /* 语言切换按钮样式 */
    .language-switcher {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .lang-btn {
      padding: 8px 16px;
      border: 2px solid var(--accent);
      background-color: transparent;
      color: var(--accent);
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      letter-spacing: 0.5px;
    }

    .lang-btn:hover {
      background-color: var(--accent);
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 200, 83, 0.3);
    }

    .lang-btn.active {
      background-color: var(--accent);
      color: white;
      box-shadow: 0 2px 8px rgba(0, 200, 83, 0.2);
    }

    .lang-btn:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(0, 200, 83, 0.3);
    }

    /* 响应式设计优化 */
    @media (max-width: 768px) {
      .hero h1 {
        font-size: 2.5rem;
      }

      .hero p {
        font-size: 1.1rem;
      }

      .contact h2 {
        font-size: 2rem;
      }

      .contact-methods {
        flex-direction: column;
        gap: 30px;
      }

      .features {
        grid-template-columns: 1fr;
      }

      .feature-card {
        padding: 25px;
      }

      .contact-container {
        width: 90%;
      }

      .email-container {
        width: 100%;
        max-width: 300px;
      }

      .language-switcher {
        gap: 6px;
      }

      .lang-btn {
        padding: 6px 12px;
        font-size: 12px;
      }

      /* 英文版移动端调整 */
      body.lang-en .hero h1 {
        font-size: 2.2rem;
        line-height: 1.2;
      }

      body.lang-en .hero p {
        font-size: 1.05rem;
      }

      body.lang-en .hero p.highlight-text {
        font-size: 1.2rem;
        padding: 10px 20px;
      }

      body.lang-en .contact h2 {
        font-size: 1.8rem;
      }

      body.lang-en .contact p {
        font-size: 1rem;
      }

      body.lang-en .feature-card h3 {
        font-size: 1.3rem;
      }

      body.lang-en .feature-card p {
        font-size: 0.9rem;
      }
    }

    @media (max-width: 480px) {
      .hero h1 {
        font-size: 2rem;
      }

      .hero p {
        font-size: 1rem;
      }

      .cta-button {
        padding: 12px 24px;
        font-size: 1rem;
      }

      .subtitle {
        font-size: 12px;
      }

      .contact h2 {
        font-size: 1.8rem;
      }

      .contact p {
        font-size: 1rem;
      }

      .qr-code-container img {
        width: 120px;
        height: 120px;
      }

      .email-contact {
        padding: 8px 16px;
        font-size: 1.1rem;
      }

      /* 英文版小屏幕调整 */
      body.lang-en .hero h1 {
        font-size: 1.8rem;
        line-height: 1.2;
      }

      body.lang-en .hero p {
        font-size: 0.95rem;
      }

      body.lang-en .hero p.highlight-text {
        font-size: 1.1rem;
        padding: 8px 16px;
      }

      body.lang-en .contact h2 {
        font-size: 1.6rem;
      }

      body.lang-en .contact p {
        font-size: 0.95rem;
      }

      body.lang-en .cta-button {
        font-size: 0.95rem;
      }

      body.lang-en .subtitle {
        font-size: 11px;
      }

      body.lang-en .email-contact {
        font-size: 1.05rem;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <header role="banner">
      <div class="header-content">
        <div class="header-left">
          <div class="qr-code-container2">
            <img src="./icons/logo.svg" alt="丰选科技logo" data-i18n-alt="logoAlt" width="100" height="50" fetchpriority="high">
          </div>
          <!-- <div class="logo">丰选科技</div> -->
          <div class="subtitle" data-i18n="subtitle">互联网产品全生命周期伙伴</div>
        </div>
        <div class="language-switcher">
          <button class="lang-btn active" data-lang="zh" aria-label="切换到中文">中文</button>
          <button class="lang-btn" data-lang="en" aria-label="Switch to English">English</button>
        </div>
      </div>
      <!-- 导航栏已移除 -->
    </header>

    <main role="main">
      <section class="hero" aria-labelledby="hero-heading">
        <div class="hero-content">
          <h1 id="hero-heading" data-i18n="heroTitle">数字产品 · 全程价值</h1>
          <p data-i18n="heroDesc1">丰选驱动互联网产品从规划到运维的全过程；</p>
          <p data-i18n="heroDesc2">真正的洞察与定制，源于实时沟通。</p>
          <p class="highlight-text" data-i18n="heroHighlight">这里仅为起点，期待与您深入对话</p>
          <a href="#contact" class="cta-button" aria-label="开始交流" data-i18n="ctaButton">开始交流</a>
        </div>
      </section>

      <section id="services" class="features" aria-label="我们的服务">
        <article class="feature-card">
          <div class="feature-icon"><img src="icons/analysis.svg" alt="需求分析图标" data-i18n-alt="analysisAlt" width="48" height="48" loading="lazy"></div>
          <h3 data-i18n="serviceTitle1">需求分析</h3>
          <p data-i18n="serviceDesc1">深入理解您的业务需求，提供专业的技术咨询和解决方案</p>
        </article>
        <article class="feature-card">
          <div class="feature-icon"><img src="icons/development.svg" alt="产品开发图标" data-i18n-alt="developmentAlt" width="48" height="48" loading="lazy"></div>
          <h3 data-i18n="serviceTitle2">产品开发</h3>
          <p data-i18n="serviceDesc2">高效开发，确保产品质量，按时交付符合您期望的数字产品</p>
        </article>
        <article class="feature-card">
          <div class="feature-icon"><img src="icons/support.svg" alt="持续支持图标" data-i18n-alt="supportAlt" width="48" height="48" loading="lazy"></div>
          <h3 data-i18n="serviceTitle3">持续支持</h3>
          <p data-i18n="serviceDesc3">提供长期技术支持和产品维护，确保您的产品持续创造价值</p>
        </article>
      </section>

      <section id="contact" class="contact" aria-labelledby="contact-heading">
        <h2 id="contact-heading" data-i18n="contactTitle">网站信息有限 · 沟通创造无限</h2>
        <p data-i18n="contactDesc1">您的需求独一无二，而标准化的技术服务难以完全契合；</p>
        <p data-i18n="contactDesc2">通过实时沟通，我们能更快理解您的目标，共同探讨最佳解决方案。</p>
        <div class="contact-container">
          <div class="contact-methods">
            <div class="contact-method">
              <div class="qr-code-container">
                <img src="./icons/qr.png" alt="丰选科技微信二维码" data-i18n-alt="qrAlt" width="140" height="140" loading="lazy">
              </div>
              <span data-i18n="wechatText">微信扫码在线沟通</span>
            </div>
          </div>

          <div class="contact-divider">
            <span data-i18n="orText">或</span>
          </div>

          <div class="email-container">
            <p data-i18n="emailText">通过邮箱联系我们:</p>
            <a href="mailto:<EMAIL>" class="email-contact" aria-label="发送邮件至*******************"><EMAIL></a>
          </div>
        </div>
      </section>
    </main>

    <footer role="contentinfo">
      <p><span data-i18n="footerText">© 2013-2025 丰选科技 版权所有ㅤㅤㅤㅤ</span><a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener" style="color: white; text-decoration: none;">津ICP备20000488号-2</a></p>
      <div style="margin-top: 10px; font-size: 0.8rem;">
        <a href="https://www.fengxuan.cn/sitemap.xml" style="color: white; text-decoration: none; margin: 0 10px;" data-i18n="sitemapText">网站地图</a>
        <a href="#" style="color: white; text-decoration: none; margin: 0 10px;" data-i18n="backToTopText">返回顶部</a>
      </div>
    </footer>
  </div>
  
  <!-- 结构化数据 -->
  <script type="application/ld+json" id="structured-data">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "丰选科技",
    "description": "互联网产品全生命周期伙伴，提供从需求分析、产品开发到持续支持的全方位服务",
    "url": "https://www.fengxuan.cn",
    "email": "<EMAIL>",
    "logo": "https://www.fengxuan.cn/icons/logo.svg",
    "service": [
      {
        "@type": "Service",
        "name": "需求分析",
        "description": "深入理解您的业务需求，提供专业的技术咨询和解决方案"
      },
      {
        "@type": "Service",
        "name": "产品开发",
        "description": "高效开发，确保产品质量，按时交付符合您期望的数字产品"
      },
      {
        "@type": "Service",
        "name": "持续支持",
        "description": "提供长期技术支持和产品维护，确保您的产品持续创造价值"
      }
    ]
  }
  </script>

  <!-- 多语言配置和切换功能 -->
  <script>
    // 语言配置对象
    const translations = {
      zh: {
        subtitle: "互联网产品全生命周期伙伴",
        heroTitle: "数字产品 · 全程价值",
        heroDesc1: "丰选驱动互联网产品从规划到运维的全过程；",
        heroDesc2: "真正的洞察与定制，源于实时沟通。",
        heroHighlight: "这里仅为起点，期待与您深入对话",
        ctaButton: "开始交流",
        serviceTitle1: "需求分析",
        serviceDesc1: "深入理解您的业务需求，提供专业的技术咨询和解决方案",
        serviceTitle2: "产品开发",
        serviceDesc2: "高效开发，确保产品质量，按时交付符合您期望的数字产品",
        serviceTitle3: "持续支持",
        serviceDesc3: "提供长期技术支持和产品维护，确保您的产品持续创造价值",
        contactTitle: "网站信息有限 · 沟通创造无限",
        contactDesc1: "您的需求独一无二，而标准化的技术服务难以完全契合；",
        contactDesc2: "通过实时沟通，我们能更快理解您的目标，共同探讨最佳解决方案。",
        wechatText: "微信扫码在线沟通",
        orText: "或",
        emailText: "通过邮箱联系我们:",
        footerText: "© 2013-2025 丰选科技 版权所有ㅤㅤㅤㅤ",
        sitemapText: "网站地图",
        backToTopText: "返回顶部",
        logoAlt: "丰选科技logo",
        analysisAlt: "需求分析图标",
        developmentAlt: "产品开发图标",
        supportAlt: "持续支持图标",
        qrAlt: "丰选科技微信二维码"
      },
      en: {
        subtitle: "Full Lifecycle Partner for Digital Products",
        heroTitle: "Digital Products · Complete Value",
        heroDesc1: "FengXuan drives the entire lifecycle of digital products from planning to operations;",
        heroDesc2: "True insights and customization emerge from real-time communication.",
        heroHighlight: "This is just the beginning - we look forward to deeper conversations with you",
        ctaButton: "Start Conversation",
        serviceTitle1: "Requirements Analysis",
        serviceDesc1: "Deep understanding of your business needs, providing professional technical consulting and tailored solutions",
        serviceTitle2: "Product Development",
        serviceDesc2: "Efficient development with quality assurance, delivering digital products that exceed your expectations on schedule",
        serviceTitle3: "Ongoing Support",
        serviceDesc3: "Comprehensive technical support and product maintenance to ensure your solutions continue creating lasting value",
        contactTitle: "Limited Website Info · Unlimited Communication Potential",
        contactDesc1: "Your needs are unique, and standardized technical services rarely provide the perfect fit;",
        contactDesc2: "Through direct communication, we can quickly understand your objectives and collaborate on optimal solutions.",
        wechatText: "Scan WeChat QR Code for Live Chat",
        orText: "or",
        emailText: "Contact us via email:",
        footerText: "© 2013-2025 FengXuan Technology. All Rights Reserved ㅤㅤㅤㅤ",
        sitemapText: "Sitemap",
        backToTopText: "Back to Top",
        logoAlt: "FengXuan Technology Logo",
        analysisAlt: "Requirements Analysis Icon",
        developmentAlt: "Product Development Icon",
        supportAlt: "Ongoing Support Icon",
        qrAlt: "FengXuan Technology WeChat QR Code"
      }
    };

    // SEO元数据配置
    const metaData = {
      zh: {
        title: "丰选科技 - 互联网产品全生命周期伙伴",
        description: "丰选科技是互联网产品全生命周期伙伴，提供从需求分析、产品开发到持续支持的全方位服务，助力产品创造持续价值。",
        keywords: "丰选,丰选科技,互联网产品,需求分析,产品开发,小程序开发,AI工具开发,数字产品,技术咨询,技术开发,技术运维",
        ogTitle: "丰选科技 - 互联网产品全生命周期伙伴",
        ogDescription: "丰选科技驱动互联网产品从规划到运维的全过程，提供真正的洞察与定制服务。",
        twitterTitle: "丰选科技 - 互联网产品全生命周期伙伴",
        twitterDescription: "丰选科技驱动互联网产品从规划到运维的全过程，提供真正的洞察与定制服务。"
      },
      en: {
        title: "FengXuan Technology - Full Lifecycle Partner for Digital Products",
        description: "FengXuan Technology is your full lifecycle partner for digital products, providing comprehensive services from requirements analysis and product development to ongoing support, helping products create lasting value.",
        keywords: "FengXuan,FengXuan Technology,digital products,requirements analysis,product development,mini-program development,AI tool development,digital solutions,technical consulting,software development,technical operations",
        ogTitle: "FengXuan Technology - Full Lifecycle Partner for Digital Products",
        ogDescription: "FengXuan drives the complete lifecycle of digital products from planning to operations, providing true insights and customized services.",
        twitterTitle: "FengXuan Technology - Full Lifecycle Partner for Digital Products",
        twitterDescription: "FengXuan drives the complete lifecycle of digital products from planning to operations, providing true insights and customized services."
      }
    };

    // 结构化数据配置
    const structuredData = {
      zh: {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "丰选科技",
        "description": "互联网产品全生命周期伙伴，提供从需求分析、产品开发到持续支持的全方位服务",
        "url": "https://www.fengxuan.cn",
        "email": "<EMAIL>",
        "logo": "https://www.fengxuan.cn/icons/logo.svg",
        "service": [
          {
            "@type": "Service",
            "name": "需求分析",
            "description": "深入理解您的业务需求，提供专业的技术咨询和解决方案"
          },
          {
            "@type": "Service",
            "name": "产品开发",
            "description": "高效开发，确保产品质量，按时交付符合您期望的数字产品"
          },
          {
            "@type": "Service",
            "name": "持续支持",
            "description": "提供长期技术支持和产品维护，确保您的产品持续创造价值"
          }
        ]
      },
      en: {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "FengXuan Technology",
        "description": "Full lifecycle partner for digital products, providing comprehensive services from requirements analysis and product development to ongoing support",
        "url": "https://www.fengxuan.cn",
        "email": "<EMAIL>",
        "logo": "https://www.fengxuan.cn/icons/logo.svg",
        "service": [
          {
            "@type": "Service",
            "name": "Requirements Analysis",
            "description": "Deep understanding of your business needs, providing professional technical consulting and tailored solutions"
          },
          {
            "@type": "Service",
            "name": "Product Development",
            "description": "Efficient development with quality assurance, delivering digital products that exceed your expectations on schedule"
          },
          {
            "@type": "Service",
            "name": "Ongoing Support",
            "description": "Comprehensive technical support and product maintenance to ensure your solutions continue creating lasting value"
          }
        ]
      }
    };

    // 当前语言
    let currentLang = 'zh';

    // 更新页面文本内容
    function updatePageContent(lang) {
      const texts = translations[lang];

      // 更新所有带有data-i18n属性的元素
      document.querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        if (texts[key]) {
          element.textContent = texts[key];
        }
      });

      // 更新所有带有data-i18n-alt属性的图片alt文本
      document.querySelectorAll('[data-i18n-alt]').forEach(element => {
        const key = element.getAttribute('data-i18n-alt');
        if (texts[key]) {
          element.setAttribute('alt', texts[key]);
        }
      });
    }

    // 更新SEO元数据
    function updateMetaData(lang) {
      const meta = metaData[lang];

      // 更新页面标题
      document.title = meta.title;

      // 更新html lang属性
      document.documentElement.setAttribute('lang', lang === 'zh' ? 'zh-CN' : 'en');

      // 更新meta标签
      document.querySelector('meta[name="description"]').setAttribute('content', meta.description);
      document.querySelector('meta[name="keywords"]').setAttribute('content', meta.keywords);
      document.querySelector('meta[property="og:title"]').setAttribute('content', meta.ogTitle);
      document.querySelector('meta[property="og:description"]').setAttribute('content', meta.ogDescription);
      document.querySelector('meta[name="twitter:title"]').setAttribute('content', meta.twitterTitle);
      document.querySelector('meta[name="twitter:description"]').setAttribute('content', meta.twitterDescription);
    }

    // 更新结构化数据
    function updateStructuredData(lang) {
      const data = structuredData[lang];
      const scriptElement = document.getElementById('structured-data');
      scriptElement.textContent = JSON.stringify(data, null, 2);
    }

    // 完整的语言切换功能
    function switchLanguage(lang) {
      currentLang = lang;

      // 更新body类名（用于样式切换）
      document.body.className = lang === 'en' ? 'lang-en' : '';

      // 更新按钮状态
      document.querySelectorAll('.lang-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.lang === lang);
      });

      // 更新页面内容
      updatePageContent(lang);

      // 更新SEO元数据
      updateMetaData(lang);

      // 更新结构化数据
      updateStructuredData(lang);

      // 保存语言偏好到localStorage
      localStorage.setItem('preferred-language', lang);

      console.log('Language switched to:', lang);
    }

    // 检测浏览器语言
    function detectBrowserLanguage() {
      const browserLang = navigator.language || navigator.userLanguage;
      return browserLang.startsWith('en') ? 'en' : 'zh';
    }

    // 获取初始语言
    function getInitialLanguage() {
      // 优先使用localStorage中保存的语言偏好
      const savedLang = localStorage.getItem('preferred-language');
      if (savedLang && (savedLang === 'zh' || savedLang === 'en')) {
        return savedLang;
      }

      // 其次使用浏览器语言检测
      return detectBrowserLanguage();
    }

    // 完整的语言系统初始化
    function initLanguage() {
      // 添加按钮点击事件
      document.querySelectorAll('.lang-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.preventDefault();
          switchLanguage(btn.dataset.lang);
        });
      });

      // 添加键盘快捷键支持（可选）
      document.addEventListener('keydown', (e) => {
        if (e.altKey && e.key === 'l') {
          e.preventDefault();
          const newLang = currentLang === 'zh' ? 'en' : 'zh';
          switchLanguage(newLang);
        }
      });

      // 设置初始语言
      const initialLang = getInitialLanguage();
      switchLanguage(initialLang);

      console.log('Language system initialized with language:', initialLang);
      console.log('Tip: Press Alt+L to toggle language');
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', initLanguage);
  </script>
</body>

</html>