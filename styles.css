/* 全局样式优化 */
body {
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

/* 导航栏美化 */
header {
    background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
    padding: 20px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

nav {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.logo {
    font-size: 24px;
    font-weight: 700;
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logo img {
    height: 40px;
    margin-right: 10px;
    border-radius: 5px;
}

.nav-links {
    display: flex;
    gap: 30px;
}

.nav-links a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 4px;
}

.nav-links a:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* 主要内容区域美化 */
main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

section {
    margin-bottom: 60px;
    background-color: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

section:hover {
    transform: translateY(-5px);
}

h1, h2, h3 {
    color: #2c3e50;
    margin-top: 0;
}

h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-align: center;
    position: relative;
    padding-bottom: 15px;
}

h1::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(to right, #1a2a6c, #b21f1f, #fdbb2d);
}

h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    border-left: 4px solid #1a2a6c;
    padding-left: 15px;
}

/* 英雄区域美化 */
#hero {
    text-align: center;
    padding: 80px 20px;
    background: linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9)), 
                url('https://source.unsplash.com/random/1600x900/?technology') no-repeat center center;
    background-size: cover;
    border-radius: 8px;
    margin-bottom: 60px;
}

#hero h1 {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #1a2a6c;
}

#hero p {
    font-size: 1.2rem;
    max-width: 800px;
    margin: 0 auto 30px;
    color: #555;
}

.cta-button {
    display: inline-block;
    background: linear-gradient(135deg, #1a2a6c, #b21f1f);
    color: white;
    padding: 12px 30px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

/* 产品特点美化 */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #1a2a6c;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #2c3e50;
}

/* 关于我们美化 */
#about {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    align-items: center;
}

.about-content {
    flex: 1;
    min-width: 300px;
}

.about-image {
    flex: 1;
    min-width: 300px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.about-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.5s ease;
}

.about-image:hover img {
    transform: scale(1.05);
}

/* 联系我们美化 */
#contact {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
}

input, textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: all 0.3s ease;
}

input:focus, textarea:focus {
    border-color: #1a2a6c;
    box-shadow: 0 0 0 3px rgba(26, 42, 108, 0.2);
    outline: none;
}

button {
    background: linear-gradient(135deg, #1a2a6c, #b21f1f);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 30px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: block;
    width: 100%;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

button:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

/* 页脚美化 */
footer {
    background-color: #2c3e50;
    color: white;
    text-align: center;
    padding: 30px 20px;
    margin-top: 60px;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 30px;
}

.footer-section {
    flex: 1;
    min-width: 250px;
}

.footer-section h3 {
    color: white;
    margin-bottom: 20px;
    font-size: 1.2rem;
    position: relative;
    padding-bottom: 10px;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: #fdbb2d;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: #ddd;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #fdbb2d;
}

.social-icons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-icons a {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.social-icons a:hover {
    background-color: #fdbb2d;
    transform: translateY(-3px);
}

.copyright {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    width: 100%;
    text-align: center;
    color: #aaa;
    font-size: 0.9rem;
}

/* 响应式设计优化 */
@media (max-width: 768px) {
    .nav-links {
        gap: 15px;
    }
    
    .features {
        grid-template-columns: 1fr;
    }
    
    #hero {
        padding: 60px 20px;
    }
    
    #hero h1 {
        font-size: 2.5rem;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.8rem;
    }
    
    section {
        padding: 25px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

section {
    animation: fadeIn 0.8s ease-out forwards;
}

section:nth-child(2) { animation-delay: 0.2s; }
section:nth-child(3) { animation-delay: 0.4s; }
section:nth-child(4) { animation-delay: 0.6s; }